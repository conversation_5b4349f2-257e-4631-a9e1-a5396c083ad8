!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("api/session-token",[],t):"object"==typeof exports?exports["api/session-token"]=t():e["api/session-token"]=t()}(this,(()=>{return e={198:(e,t,o)=>{async function s(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{const s=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/authen/sessiontoken`,n={...e.headers};if(delete n.host,delete n.connection,n["Content-Type"]="application/json",n.Accept="application/json",!n.Authorization&&process.env.JWT_TOKEN&&(n.Authorization=`Bearer ${process.env.JWT_TOKEN}`),!n["X-Ekyc-Device-Info"]){const e=o(687).randomUUID();n["X-Ekyc-Device-Info"]=`browser|${e}`}const r=await fetch(s,{method:"GET",headers:n}),i=await r.json();return t.status(r.status).json(i)}catch(e){return console.error("Error getting session token:",e),t.status(500).json({error:"Failed to get session token"})}}e.exports=s,e.exports.default=s},687:()=>{}},t={},function o(s){var n=t[s];if(void 0!==n)return n.exports;var r=t[s]={exports:{}};return e[s](r,r.exports,o),r.exports}(198);var e,t}));