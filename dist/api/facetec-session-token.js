!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("api/facetec-session-token",[],t):"object"==typeof exports?exports["api/facetec-session-token"]=t():e["api/facetec-session-token"]=t()}(this,(()=>{return e={264:(e,t,o)=>{async function s(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{const s=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/authen/sessiontoken/facetec`,n={...e.headers};if(delete n.host,delete n.connection,n["Content-Type"]="application/json",n.Accept="application/json",!n.Authorization&&process.env.JWT_TOKEN&&(n.Authorization=`Bearer ${process.env.JWT_TOKEN}`),!n["X-Ekyc-Device-Info"]){const e=o(687).randomUUID();n["X-Ekyc-Device-Info"]=`browser|${e}`}const r=await fetch(s,{method:"GET",headers:n}),c=await r.json();return t.status(r.status).json(c)}catch(e){return console.error("Error getting FaceTec session token:",e),t.status(500).json({error:"Failed to get FaceTec session token"})}}e.exports=s,e.exports.default=s},687:()=>{}},t={},function o(s){var n=t[s];if(void 0!==n)return n.exports;var r=t[s]={exports:{}};return e[s](r,r.exports,o),r.exports}(264);var e,t}));