/**
 * eKYC SDK TypeScript Definitions
 * Framework-agnostic eKYC SDK with FaceTec integration by SCB TechX
 *
 * Usage with local files:
 * import EkycSDK from './ekyc-sdk/ekyc-sdk.js';
 * import { Auth, FaceTec, Utils } from './ekyc-sdk/ekyc-sdk.js';
 */

export interface SessionTokenResponse {
  code: string;
  description?: string;
  data?: {
    ekycToken: string;
    sessionId: string;
    deviceKey?: string;
    encryptionKey?: string;
    sessionFaceTec?: string;
  };
  expiresAt?: string;
  faceTecInitialized?: boolean;
  faceTecError?: string;
}

export interface IDScanResult {
  sessionResult: any;
  idScanResult: any;
  networkResponseStatus: number;
}

export interface ScanHeaders {
  'X-Session-Id'?: string;
  'X-Ekyc-Token'?: string;
  'correlationid'?: string;
  'Authorization'?: string;
  [key: string]: string | undefined;
}

export interface AuthOptions {
  headers?: Record<string, string>;
  apiKey?: string;
  storeToken?: boolean;
}

export interface FaceTecOptions {
  headers?: ScanHeaders;
  initializeFaceTec?: boolean;
}

export interface IDScanOptions {
  deviceKey: string;
  sessionTokenResponse: SessionTokenResponse;
  headers?: ScanHeaders;
}

export interface FaceTecWorkflowConfig {
  deviceKey: string;
  encryptionKey: string;
  sessionTokenResponse: SessionTokenResponse;
  scanHeaders?: ScanHeaders;
}

export interface WorkflowResult {
  success: boolean;
  scanResult?: IDScanResult;
  error?: string;
  workflow: 'complete' | 'failed';
}

/**
 * Authentication namespace
 */
export declare namespace Auth {
  function getSessionToken(options?: AuthOptions): Promise<SessionTokenResponse>;
  function getFaceTecSessionToken(options?: FaceTecOptions): Promise<SessionTokenResponse>;
  function getStoredToken(): string | null;
  function clearToken(): boolean;
}

/**
 * FaceTec namespace
 */
export declare namespace FaceTec {
  function initialize(deviceKey: string, encryptionKey: string): Promise<boolean>;
  function loadSDK(): Promise<any>;
  function getVersion(): Promise<string>;
  function performIDScan(options: IDScanOptions): Promise<IDScanResult>;
}

/**
 * Utilities namespace
 */
export declare namespace Utils {
  function generateUUID(): string;
  function getDeviceId(): string;
  
  namespace TokenStorage {
    function store(key: string, value: string): boolean;
    function get(key: string): string | null;
    function remove(key: string): boolean;
  }
}

/**
 * Main SDK interface
 */
export interface EkycSDKInterface {
  Auth: typeof Auth;
  FaceTec: typeof FaceTec;
  Utils: typeof Utils;
  
  // Quick access methods
  getSessionToken(apiKey: string, options?: Omit<AuthOptions, 'apiKey'>): Promise<SessionTokenResponse>;
  initializeFaceTec(sessionTokenResponse: SessionTokenResponse): Promise<boolean>;
  performIDScan(deviceKey: string, sessionTokenResponse: SessionTokenResponse, options?: Omit<IDScanOptions, 'deviceKey' | 'sessionTokenResponse'>): Promise<IDScanResult>;
  
  // Metadata
  version: string;
  name: string;
}

/**
 * FaceTec Module interface (for separate import)
 */
export interface FaceTecModuleInterface {
  initialize(deviceKey: string, encryptionKey: string): Promise<boolean>;
  loadSDK(): Promise<any>;
  getVersion(): Promise<string>;
  performIDScan(config: IDScanOptions): Promise<IDScanResult>;
  isAvailable(): boolean;
  setResourceDirectories(resourceDir?: string, imagesDir?: string): Promise<void>;
  performCompleteWorkflow(config: FaceTecWorkflowConfig): Promise<WorkflowResult>;
}

/**
 * API Handler types (for server-side usage)
 */
export interface NextApiRequest {
  method: string;
  headers: Record<string, string>;
  body?: any;
}

export interface NextApiResponse {
  status(code: number): {
    json(data: any): any;
  };
  json(data: any): any;
}

export type ApiHandler = (req: NextApiRequest, res: NextApiResponse) => Promise<any>;

export interface ApiHandlers {
  sessionTokenHandler: ApiHandler;
  facetecSessionTokenHandler: ApiHandler;
  idscanOnlyHandler: ApiHandler;
  healthHandler: ApiHandler;
}

// Main exports
declare const EkycSDK: EkycSDKInterface;
declare const FaceTecModule: FaceTecModuleInterface;

export default EkycSDK;
export { EkycSDK, FaceTecModule };
export { Auth, FaceTec, Utils };

// API handlers export
export * as ApiHandlers from './api';

/**
 * Framework-specific integration helpers
 */
export declare namespace Integrations {
  namespace React {
    interface UseEkycOptions extends AuthOptions {
      autoInitialize?: boolean;
    }
    
    interface UseEkycReturn {
      sessionToken: SessionTokenResponse | null;
      isLoading: boolean;
      error: string | null;
      getSessionToken: (apiKey: string) => Promise<void>;
      initializeFaceTec: () => Promise<void>;
      performIDScan: (deviceKey: string) => Promise<IDScanResult>;
    }
    
    function useEkyc(options?: UseEkycOptions): UseEkycReturn;
  }
  
  namespace Vue {
    interface EkycPluginOptions {
      apiKey?: string;
      autoInitialize?: boolean;
    }
    
    function install(app: any, options?: EkycPluginOptions): void;
  }
  
  namespace Angular {
    class EkycService {
      getSessionToken(apiKey: string, options?: AuthOptions): Promise<SessionTokenResponse>;
      initializeFaceTec(sessionTokenResponse: SessionTokenResponse): Promise<boolean>;
      performIDScan(deviceKey: string, sessionTokenResponse: SessionTokenResponse): Promise<IDScanResult>;
    }
  }
}
