/**
 * eKYC SDK - Main Bundle
 * SCB TechX eKYC SDK v1.0.0
 * 
 * This is the main SDK bundle that includes the 5 main entry point functions
 * for external developers using the eKYC SDK.
 */

// Import the SDK implementation
const EkycSDKCore = require('../lib/sdk-entry');

// Export the SDK with all functionality
module.exports = EkycSDKCore;
module.exports.default = EkycSDKCore;

// For ES6 modules
if (typeof exports !== 'undefined') {
  exports.EkycSDK = EkycSDKCore;
  exports.Auth = EkycSDKCore.Auth;
  exports.FaceTec = EkycSDKCore.FaceTec;
  exports.Utils = EkycSDKCore.Utils;
}

// For browser/UMD usage
if (typeof window !== 'undefined') {
  window.EkycSDK = EkycSDKCore;
  window.ScbTechXEkycSDK = EkycSDKCore;
}

// For AMD
if (typeof define === 'function' && define.amd) {
  define('ekyc-sdk', [], function() {
    return EkycSDKCore;
  });
}
