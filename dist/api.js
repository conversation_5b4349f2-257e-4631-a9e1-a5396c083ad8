!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("api",[],t):"object"==typeof exports?exports.api=t():e.api=t()}(this,(()=>{return e={154:(e,t,o)=>{const n=o(198),s=o(264);e.exports={sessionTokenHandler:n,facetecSessionTokenHandler:s}},198:(e,t,o)=>{async function n(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{const n=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/authen/sessiontoken`,s={...e.headers};if(delete s.host,delete s.connection,s["Content-Type"]="application/json",s.Accept="application/json",!s.Authorization&&process.env.JWT_TOKEN&&(s.Authorization=`Bearer ${process.env.JWT_TOKEN}`),!s["X-Ekyc-Device-Info"]){const e=o(687).randomUUID();s["X-Ekyc-Device-Info"]=`browser|${e}`}const r=await fetch(n,{method:"GET",headers:s}),c=await r.json();return t.status(r.status).json(c)}catch(e){return console.error("Error getting session token:",e),t.status(500).json({error:"Failed to get session token"})}}e.exports=n,e.exports.default=n},264:(e,t,o)=>{async function n(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{const n=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/authen/sessiontoken/facetec`,s={...e.headers};if(delete s.host,delete s.connection,s["Content-Type"]="application/json",s.Accept="application/json",!s.Authorization&&process.env.JWT_TOKEN&&(s.Authorization=`Bearer ${process.env.JWT_TOKEN}`),!s["X-Ekyc-Device-Info"]){const e=o(687).randomUUID();s["X-Ekyc-Device-Info"]=`browser|${e}`}const r=await fetch(n,{method:"GET",headers:s}),c=await r.json();return t.status(r.status).json(c)}catch(e){return console.error("Error getting FaceTec session token:",e),t.status(500).json({error:"Failed to get FaceTec session token"})}}e.exports=n,e.exports.default=n},687:()=>{}},t={},function o(n){var s=t[n];if(void 0!==s)return s.exports;var r=t[n]={exports:{}};return e[n](r,r.exports,o),r.exports}(154);var e,t}));