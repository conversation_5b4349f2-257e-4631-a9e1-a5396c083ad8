!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("api",[],t):"object"==typeof exports?exports.api=t():e.api=t()}(this,(()=>{return e={22:e=>{function t(e,t){t.status(200).json({status:"UP"})}e.exports=t,e.exports.default=t},154:(e,t,o)=>{const s=o(198),n=o(264),r=o(235),c=o(22);e.exports={sessionTokenHandler:s,facetecSessionTokenHandler:n,idscanOnlyHandler:r,healthHandler:c}},198:(e,t,o)=>{const s=o(599);async function n(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{const o=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/authen/sessiontoken`,n={...e.headers};if(delete n.host,delete n.connection,n["Content-Type"]="application/json",n.Accept="application/json",!n.Authorization&&process.env.JWT_TOKEN&&(n.Authorization=`Bearer ${process.env.JWT_TOKEN}`),!n["X-Ekyc-Device-Info"]){const e=s.getUniqueId();n["X-Ekyc-Device-Info"]=`browser|${e}`}const r=await fetch(o,{method:"GET",headers:n}),c=await r.json();return t.status(r.status).json(c)}catch(e){return console.error("Error getting session token:",e),t.status(500).json({error:"Failed to get session token"})}}e.exports=n,e.exports.default=n},235:e=>{async function t(e,t){if("POST"!==e.method)return t.status(405).json({error:"Method not allowed"});try{const o=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/idscan-only`,s={"Content-Type":"application/json",Accept:"application/json",Authorization:e.headers.authorization||(process.env.JWT_TOKEN?`Bearer ${process.env.JWT_TOKEN}`:void 0),"X-Device-Key":e.headers["x-device-key"],"X-User-Agent":e.headers["x-user-agent"],"X-Session-Id":e.headers["x-session-id"],"X-Tid":e.headers["x-tid"],"X-Ekyc-Token":e.headers["x-ekyc-token"],correlationid:e.headers.correlationid};Object.keys(s).forEach((e=>{void 0===s[e]&&delete s[e]}));const n=await fetch(o,{method:"POST",headers:s,body:JSON.stringify(e.body)}),r=await n.json();if(n.ok&&"CUS-KYC-1000"===r.code){const e={wasProcessed:!0,error:!1,scanResultBlob:r.data?.scanResultBlob||"",originalResponse:r};return console.log("Returning success response with scanResultBlob length:",e.scanResultBlob.length),t.status(200).json(e)}{const e={wasProcessed:!1,error:!0,errorMessage:r.description||r.message||"Unknown error",originalResponse:r};return console.log("Returning error response:",e.errorMessage),t.status(n.status).json(e)}}catch(e){return console.error("Error processing ID scan:",e),t.status(500).json({wasProcessed:!1,error:!0,errorMessage:"Failed to process ID scan"})}}e.exports=t,e.exports.default=t},264:(e,t,o)=>{const s=o(599);async function n(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{const o=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/authen/sessiontoken/facetec`,n={...e.headers};if(delete n.host,delete n.connection,n["Content-Type"]="application/json",n.Accept="application/json",!n.Authorization&&process.env.JWT_TOKEN&&(n.Authorization=`Bearer ${process.env.JWT_TOKEN}`),!n["X-Ekyc-Device-Info"]){const e=s.getUniqueId();n["X-Ekyc-Device-Info"]=`browser|${e}`}const r=await fetch(o,{method:"GET",headers:n}),c=await r.json();return t.status(r.status).json(c)}catch(e){return console.error("Error getting FaceTec session token:",e),t.status(500).json({error:"Failed to get FaceTec session token"})}}e.exports=n,e.exports.default=n},599:e=>{e.exports=class{static generateUuid(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){const t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}static getDeviceId(){if("undefined"!=typeof window&&window.localStorage){let e=localStorage.getItem("ekyc_device_id");return e||(e=this.generateUuid(),localStorage.setItem("ekyc_device_id",e)),e}return this.generateUuid()}static getUniqueId(){return this.generateUuid()}}}},t={},function o(s){var n=t[s];if(void 0!==n)return n.exports;var r=t[s]={exports:{}};return e[s](r,r.exports,o),r.exports}(154);var e,t}));