/**
 * Dynamic API route that loads handlers from the compiled SDK
 * This allows us to remove individual API route files and use only the SDK handlers
 */

/**
 * Dynamic API handler that routes to SDK handlers
 * @param {import('next').NextApiRequest} req
 * @param {import('next').NextApiResponse} res
 */
export default async function handler(req, res) {
  const { slug } = req.query;
  const path = Array.isArray(slug) ? slug.join('/') : slug;

  try {
    let sdkHandler;

    // Route to appropriate SDK handler based on path
    switch (path) {
      case 'session-token':
        try {
          // Try to import from compiled SDK first
          const { sessionTokenHandler } = require('../../../dist/api');
          sdkHandler = sessionTokenHandler;
        } catch (error) {
          // Fallback to lib directory if dist is not available
          console.log("fallback to lib directory");
          sdkHandler = require('../../../lib/infrastructure/api/session-token');
        }
        break;
        
      case 'facetec-session-token':
        try {
          // Try to import from compiled SDK first
          const { facetecSessionTokenHandler } = require('../../../dist/api');
          sdkHandler = facetecSessionTokenHandler;
        } catch (error) {
          // Fallback to lib directory if dist is not available
          console.log("fallback to lib directory");
          sdkHandler = require('../../../lib/infrastructure/api/facetec-session-token');
        }
        break;

      case 'idscan-only':
        try {
          // Try to import from compiled SDK first
          const { idscanOnlyHandler } = require('../../../dist/api');
          sdkHandler = idscanOnlyHandler;
        } catch (error) {
          // Fallback to lib directory if dist is not available
          console.log("fallback to lib directory");
          sdkHandler = require('../../../lib/infrastructure/api/idscan-only');
        }
        break;

      case 'health':
        try {
          // Try to import from compiled SDK first
          const { healthHandler } = require('../../../dist/api');
          sdkHandler = healthHandler;
        } catch (error) {
          // Fallback to lib directory if dist is not available
          console.log("fallback to lib directory");
          sdkHandler = require('../../../lib/infrastructure/api/health');
        }
        break;
        
      default:
        return res.status(404).json({ error: 'API endpoint not found' });
    }

    if (sdkHandler) {
      // Call the SDK handler with req and res
      return await sdkHandler(req, res);
    } else {
      return res.status(404).json({ error: 'Handler not found' });
    }
  } catch (error) {
    console.error('Error in dynamic API handler:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

// Configuration for body parser (needed for idscan-only endpoint)
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '10mb',
    },
  },
}
