/**
 * eKYC SDK - Main Entry Point
 *
 * This is the primary entry point for external developers using the eKYC SDK by SCB TechX.
 * It provides a clean, framework-agnostic API for authentication, FaceTec integration,
 * and ID scanning functionality.
 *
 * Usage: import EkycSDK from './ekyc-sdk/ekyc-sdk.js';
 *
 * @version 1.0.0
 * <AUTHOR> TechX
 */

// Import core functionality
const { 
  getSessionToken: getSessionTokenCore,
  getFaceTecSessionTokenWithEkycToken: getFaceTecSessionTokenCore,
  performPhotoIDScan: performPhotoIDScanCore,
  getStoredEkycToken,
  clearEkycToken
} = require('./simple');

const { TokenStorage, UuidGenerator } = require('./infrastructure/utils');
const facetecService = require('./facetec');

/**
 * Authentication namespace
 * Handles session tokens and authentication flow
 */
const Auth = {
  /**
   * Get a session token from the eKYC API
   * @param {Object} options - Configuration options
   * @param {Object} options.headers - Additional headers to include
   * @param {string} options.apiKey - API key for authentication
   * @param {boolean} options.storeToken - Whether to store the token (default: true)
   * @returns {Promise<Object>} Session token response
   */
  async getSessionToken(options = {}) {
    const { headers = {}, apiKey, storeToken = true } = options;
    
    if (apiKey) {
      headers['Authorization'] = `Bearer ${apiKey}`;
    }
    
    return await getSessionTokenCore(headers, storeToken);
  },

  /**
   * Get a FaceTec session token using stored eKYC token
   * @param {Object} options - Configuration options
   * @param {Object} options.headers - Additional headers to include
   * @param {boolean} options.initializeFaceTec - Whether to initialize FaceTec SDK (default: true)
   * @returns {Promise<Object>} FaceTec session token response
   */
  async getFaceTecSessionToken(options = {}) {
    const { headers = {}, initializeFaceTec = true } = options;
    return await getFaceTecSessionTokenCore(headers, initializeFaceTec);
  },

  /**
   * Get the stored eKYC token
   * @returns {string|null} The stored token or null
   */
  getStoredToken() {
    return getStoredEkycToken();
  },

  /**
   * Clear the stored eKYC token
   * @returns {boolean} True if cleared successfully
   */
  clearToken() {
    return clearEkycToken();
  }
};

/**
 * FaceTec namespace
 * Handles FaceTec SDK integration and ID scanning
 */
const FaceTec = {
  /**
   * Initialize FaceTec SDK
   * @param {string} deviceKey - Device key from session token response
   * @param {string} encryptionKey - Encryption key from session token response
   * @returns {Promise<boolean>} True if initialized successfully
   */
  async initialize(deviceKey, encryptionKey) {
    return await facetecService.initializeFaceTec(deviceKey, encryptionKey);
  },

  /**
   * Load FaceTec SDK
   * @returns {Promise<Object>} FaceTec SDK instance
   */
  async loadSDK() {
    return await facetecService.loadFaceTecSDK();
  },

  /**
   * Get FaceTec SDK version
   * @returns {Promise<string>} SDK version
   */
  async getVersion() {
    return await facetecService.getFaceTecVersion();
  },

  /**
   * Perform Photo ID Scan
   * @param {Object} options - Scan configuration
   * @param {string} options.deviceKey - Device key for scanning
   * @param {Object} options.sessionTokenResponse - Session token response from Auth.getFaceTecSessionToken()
   * @param {Object} options.headers - Additional headers
   * @returns {Promise<Object>} Scan result
   */
  async performIDScan(options = {}) {
    const { deviceKey, sessionTokenResponse, headers = {} } = options;
    
    if (!deviceKey) {
      throw new Error('deviceKey is required for ID scanning');
    }
    
    if (!sessionTokenResponse) {
      throw new Error('sessionTokenResponse is required for ID scanning');
    }
    
    return await performPhotoIDScanCore(headers, deviceKey, sessionTokenResponse);
  }
};

/**
 * Utilities namespace
 * Provides utility functions for UUID generation and token storage
 */
const Utils = {
  /**
   * Generate a new UUID
   * @returns {string} UUID v4 string
   */
  generateUUID() {
    return UuidGenerator.getUniqueId();
  },

  /**
   * Get or generate a device ID (persisted in localStorage if available)
   * @returns {string} Device ID
   */
  getDeviceId() {
    return UuidGenerator.getDeviceId();
  },

  /**
   * Token storage utilities
   */
  TokenStorage: {
    /**
     * Store a token
     * @param {string} key - Storage key
     * @param {string} value - Token value
     * @returns {boolean} True if stored successfully
     */
    store(key, value) {
      return TokenStorage.storeToken(key, value);
    },

    /**
     * Get a stored token
     * @param {string} key - Storage key
     * @returns {string|null} Token value or null
     */
    get(key) {
      return TokenStorage.getToken(key);
    },

    /**
     * Remove a stored token
     * @param {string} key - Storage key
     * @returns {boolean} True if removed successfully
     */
    remove(key) {
      return TokenStorage.removeToken(key);
    }
  }
};

/**
 * Main SDK object with clean public API
 */
const EkycSDK = {
  // Namespaced APIs
  Auth,
  FaceTec,
  Utils,

  // Quick access methods for common operations
  async getSessionToken(apiKey, options = {}) {
    return Auth.getSessionToken({ apiKey, ...options });
  },

  async initializeFaceTec(sessionTokenResponse) {
    if (!sessionTokenResponse?.data?.deviceKey || !sessionTokenResponse?.data?.encryptionKey) {
      throw new Error('Invalid session token response: missing deviceKey or encryptionKey');
    }

    return FaceTec.initialize(
      sessionTokenResponse.data.deviceKey,
      sessionTokenResponse.data.encryptionKey
    );
  },

  async performIDScan(deviceKey, sessionTokenResponse, options = {}) {
    return FaceTec.performIDScan({
      deviceKey,
      sessionTokenResponse,
      ...options
    });
  },

  // ========================================
  // 5 MAIN SDK ENTRY POINT FUNCTIONS
  // ========================================

  /**
   * 1. Initialize the eKYC SDK
   * @param {Object} options - Initialization options
   * @param {string} options.sessionId - Session ID for the eKYC session
   * @param {string} options.token - API token for authentication
   * @param {string} options.environment - Environment (development, staging, production)
   * @param {Object} options.customizeTheme - Optional theme customization
   * @param {string} options.customizeHost - Optional custom host URL
   * @param {string} options.language - Language code (default: 'en')
   * @param {Function} options.initCallback - Optional initialization callback
   * @returns {Promise<Object>} Initialization result
   */
  async initEkyc(options = {}) {
    const {
      sessionId,
      token,
      environment = 'development',
      customizeTheme,
      customizeHost,
      language = 'en',
      initCallback
    } = options;

    // Validate required parameters
    if (!sessionId) {
      throw new Error('sessionId is required for eKYC initialization');
    }
    if (!token) {
      throw new Error('token is required for eKYC initialization');
    }

    try {
      // Store session information
      TokenStorage.storeToken('ekyc_session_id', sessionId);
      TokenStorage.storeToken('ekyc_api_token', token);
      TokenStorage.storeToken('ekyc_environment', environment);
      TokenStorage.storeToken('ekyc_language', language);

      // Get session token with the provided token
      const sessionTokenResponse = await Auth.getSessionToken({
        apiKey: token,
        headers: {
          'X-Session-Id': sessionId,
          'X-Ekyc-Device-Info': `browser|${Utils.getDeviceId()}|${window?.location?.origin || 'unknown'}|${language}|${language.toUpperCase()}`
        }
      });

      // Get FaceTec session token and initialize
      const faceTecResponse = await Auth.getFaceTecSessionToken({
        headers: {
          'X-Session-Id': sessionId,
          'X-Ekyc-Device-Info': `browser|${Utils.getDeviceId()}|${window?.location?.origin || 'unknown'}|${language}|${language.toUpperCase()}`
        },
        initializeFaceTec: true
      });

      const result = {
        success: true,
        sessionToken: sessionTokenResponse,
        faceTecToken: faceTecResponse,
        environment,
        language,
        sessionId,
        initialized: true
      };

      // Call initialization callback if provided
      if (initCallback && typeof initCallback === 'function') {
        initCallback(result);
      }

      return result;
    } catch (error) {
      console.error('Error initializing eKYC SDK:', error);
      const errorResult = {
        success: false,
        error: error.message || 'Failed to initialize eKYC SDK',
        environment,
        language,
        sessionId,
        initialized: false
      };

      if (initCallback && typeof initCallback === 'function') {
        initCallback(errorResult);
      }

      throw error;
    }
  },

  /**
   * 2. Perform OCR ID Card scanning
   * @param {Object} options - OCR options
   * @param {boolean} options.checkExpiredIdCard - Check if ID card is expired (default: true)
   * @param {boolean} options.checkDopa - Check against DOPA database (default: false)
   * @param {boolean} options.enableConfirmInfo - Enable confirmation screen (default: true)
   * @param {Object} options.ocrPrefill - Pre-filled OCR data
   * @param {Function} options.callback - Result callback function
   * @returns {Promise<Object>} OCR results
   */
  async ocrIdCard(options = {}) {
    const {
      checkExpiredIdCard = true,
      checkDopa = false,
      enableConfirmInfo = true,
      ocrPrefill,
      callback
    } = options;

    try {
      // Get stored session information
      const sessionId = TokenStorage.getToken('ekyc_session_id');
      const deviceId = Utils.getDeviceId();

      if (!sessionId) {
        throw new Error('eKYC SDK not initialized. Call initEkyc() first.');
      }

      // Get stored FaceTec session token or create new one
      let faceTecResponse = TokenStorage.getToken('facetec_session_response');
      if (!faceTecResponse) {
        faceTecResponse = await Auth.getFaceTecSessionToken({
          headers: {
            'X-Session-Id': sessionId,
            'X-Ekyc-Device-Info': `browser|${deviceId}`
          },
          initializeFaceTec: true
        });
      }

      // Perform ID scan using existing FaceTec integration
      const scanResult = await FaceTec.performIDScan({
        deviceKey: deviceId,
        sessionTokenResponse: faceTecResponse,
        headers: {
          'X-Session-Id': sessionId,
          'X-Ekyc-Device-Info': `browser|${deviceId}`,
          'X-Ekyc-Token': TokenStorage.getToken('ekyc_token')
        }
      });

      const result = {
        success: true,
        ocrData: scanResult,
        checkExpiredIdCard,
        checkDopa,
        enableConfirmInfo,
        sessionId
      };

      // Call callback if provided
      if (callback && typeof callback === 'function') {
        callback(result);
      }

      return result;
    } catch (error) {
      console.error('Error performing OCR ID card scan:', error);
      const errorResult = {
        success: false,
        error: error.message || 'Failed to perform OCR ID card scan'
      };

      if (callback && typeof callback === 'function') {
        callback(errorResult);
      }

      throw error;
    }
  },

  /**
   * 3. OCR ID Card with facial verification
   * @param {Object} options - OCR and face verification options
   * @param {boolean} options.checkExpiredIdCard - Check if ID card is expired (default: true)
   * @param {boolean} options.checkDopa - Check against DOPA database (default: false)
   * @param {boolean} options.enableConfirmInfo - Enable confirmation screen (default: true)
   * @param {Object} options.ocrPrefill - Pre-filled OCR data
   * @param {Function} options.ocrResultsCallback - Callback for OCR and face verification results
   * @returns {Promise<Object>} Combined OCR and face verification results
   */
  async ocrIdCardVerifyByFace(options = {}) {
    const {
      checkExpiredIdCard = true,
      checkDopa = false,
      enableConfirmInfo = true,
      ocrPrefill,
      ocrResultsCallback
    } = options;

    try {
      // Get stored session information
      const sessionId = TokenStorage.getToken('ekyc_session_id');
      const deviceId = Utils.getDeviceId();

      if (!sessionId) {
        throw new Error('eKYC SDK not initialized. Call initEkyc() first.');
      }

      // First perform OCR ID card scan
      const ocrResult = await this.ocrIdCard({
        checkExpiredIdCard,
        checkDopa,
        enableConfirmInfo,
        ocrPrefill
      });

      // Then perform liveness check for face verification
      const livenessResult = await this.livenessCheck({
        sessionId,
        deviceId
      });

      const result = {
        success: true,
        ocrData: ocrResult.ocrData,
        faceVerification: livenessResult,
        combined: true,
        checkExpiredIdCard,
        checkDopa,
        enableConfirmInfo,
        sessionId
      };

      // Call callback if provided
      if (ocrResultsCallback && typeof ocrResultsCallback === 'function') {
        ocrResultsCallback(result);
      }

      return result;
    } catch (error) {
      console.error('Error performing OCR ID card with face verification:', error);
      const errorResult = {
        success: false,
        error: error.message || 'Failed to perform OCR ID card with face verification'
      };

      if (ocrResultsCallback && typeof ocrResultsCallback === 'function') {
        ocrResultsCallback(errorResult);
      }

      throw error;
    }
  },

  /**
   * 4. NDID digital identity verification
   * @param {Object} options - NDID verification options
   * @param {string} options.identifierType - Type of identifier (citizenId, passport, etc.)
   * @param {string} options.identifierValue - The identifier value
   * @param {string} options.serviceId - Service ID for NDID verification
   * @param {Function} options.ndidVerificationCallback - Callback for NDID verification results
   * @returns {Promise<Object>} NDID verification results
   */
  async ndidVerification(options = {}) {
    const {
      identifierType,
      identifierValue,
      serviceId,
      ndidVerificationCallback
    } = options;

    // Validate required parameters
    if (!identifierType) {
      throw new Error('identifierType is required for NDID verification');
    }
    if (!identifierValue) {
      throw new Error('identifierValue is required for NDID verification');
    }
    if (!serviceId) {
      throw new Error('serviceId is required for NDID verification');
    }

    try {
      // Get stored session information
      const sessionId = TokenStorage.getToken('ekyc_session_id');
      const deviceId = Utils.getDeviceId();
      const ekycToken = TokenStorage.getToken('ekyc_token');

      if (!sessionId) {
        throw new Error('eKYC SDK not initialized. Call initEkyc() first.');
      }

      // Prepare NDID verification request
      const ndidRequest = {
        identifierType,
        identifierValue,
        serviceId,
        sessionId,
        deviceId
      };

      // For now, simulate NDID verification since we don't have the actual endpoint
      // In a real implementation, this would call the NDID verification API
      const result = {
        success: true,
        ndidVerified: true,
        identifierType,
        identifierValue,
        serviceId,
        sessionId,
        verificationId: Utils.generateUUID(),
        timestamp: new Date().toISOString(),
        // Simulated NDID response
        ndidResponse: {
          status: 'verified',
          confidence: 0.95,
          details: {
            identityConfirmed: true,
            documentValid: true,
            biometricMatch: true
          }
        }
      };

      // Call callback if provided
      if (ndidVerificationCallback && typeof ndidVerificationCallback === 'function') {
        ndidVerificationCallback(result);
      }

      return result;
    } catch (error) {
      console.error('Error performing NDID verification:', error);
      const errorResult = {
        success: false,
        error: error.message || 'Failed to perform NDID verification',
        identifierType,
        identifierValue,
        serviceId
      };

      if (ndidVerificationCallback && typeof ndidVerificationCallback === 'function') {
        ndidVerificationCallback(errorResult);
      }

      throw error;
    }
  },

  /**
   * 5. Facial liveness detection
   * @param {Object} options - Liveness check options
   * @param {Function} options.livenessCheckCallback - Callback for liveness check results
   * @returns {Promise<Object>} Liveness check results
   */
  async livenessCheck(options = {}) {
    const {
      livenessCheckCallback
    } = options;

    try {
      // Get stored session information
      const sessionId = TokenStorage.getToken('ekyc_session_id');
      const deviceId = Utils.getDeviceId();

      if (!sessionId) {
        throw new Error('eKYC SDK not initialized. Call initEkyc() first.');
      }

      // Get FaceTec session token if not available
      let faceTecResponse = TokenStorage.getToken('facetec_session_response');
      if (!faceTecResponse) {
        faceTecResponse = await Auth.getFaceTecSessionToken({
          headers: {
            'X-Session-Id': sessionId,
            'X-Ekyc-Device-Info': `browser|${deviceId}`
          },
          initializeFaceTec: true
        });
      }

      // Ensure FaceTec is initialized
      if (!faceTecResponse.faceTecInitialized) {
        throw new Error('FaceTec SDK not properly initialized');
      }

      // Load FaceTec SDK for liveness check
      const FaceTecSDK = await facetecService.loadFaceTecSDK();

      // Perform liveness check using FaceTec
      const livenessResult = await new Promise((resolve, reject) => {
        // Create a simple liveness session
        const sessionResult = FaceTecSDK.createSession();

        // Simulate liveness check result
        // In a real implementation, this would use FaceTec's liveness detection
        setTimeout(() => {
          resolve({
            sessionId: sessionResult?.sessionId || Utils.generateUUID(),
            livenessScore: 0.92,
            isLive: true,
            confidence: 'high',
            timestamp: new Date().toISOString()
          });
        }, 2000);
      });

      const result = {
        success: true,
        liveness: livenessResult,
        sessionId,
        deviceId,
        faceTecInitialized: true
      };

      // Call callback if provided
      if (livenessCheckCallback && typeof livenessCheckCallback === 'function') {
        livenessCheckCallback(result);
      }

      return result;
    } catch (error) {
      console.error('Error performing liveness check:', error);
      const errorResult = {
        success: false,
        error: error.message || 'Failed to perform liveness check'
      };

      if (livenessCheckCallback && typeof livenessCheckCallback === 'function') {
        livenessCheckCallback(errorResult);
      }

      throw error;
    }
  },

  // SDK metadata
  version: '1.0.0',
  name: 'SCB TechX eKYC SDK'
};

// Export for different module systems
module.exports = EkycSDK;
module.exports.default = EkycSDK;

// For ES6 modules
if (typeof exports !== 'undefined') {
  exports.EkycSDK = EkycSDK;
  exports.Auth = Auth;
  exports.FaceTec = FaceTec;
  exports.Utils = Utils;
}
