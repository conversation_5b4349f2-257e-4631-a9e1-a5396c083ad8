/**
 * API handlers for the eKYC SDK
 * These handlers provide proxy functionality for eKYC API endpoints
 * and can be used both in Next.js applications and standalone SDK implementations
 */

const sessionTokenHandler = require('./session-token');
const facetecSessionTokenHandler = require('./facetec-session-token');
const idscanOnlyHandler = require('./idscan-only');
const healthHandler = require('./health');

module.exports = {
  sessionTokenHandler,
  facetecSessionTokenHandler,
  idscanOnlyHandler,
  healthHandler
};
