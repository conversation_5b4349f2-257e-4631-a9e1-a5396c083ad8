const UuidGenerator = require('../utils/UuidGenerator');

/**
 * API route to get a session token from the eKYC authentication API
 * This acts as a proxy to avoid CORS issues when calling from the browser
 *
 * @param {import('next').NextApiRequest} req
 * @param {import('next').NextApiResponse} res
 */
async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Set the base URL - this should be configured based on environment
    const baseUrl = process.env.API_BASE_URL || 'https://ekyc-ekyc-dev.np.scbtechx.io';
    const url = `${baseUrl}/v1/ekyc/authen/sessiontoken`;

    // Forward any headers from the client request, except host-specific ones
    const headers = { ...req.headers };
    delete headers.host;
    delete headers.connection;

    // Add or override specific headers
    headers['Content-Type'] = 'application/json';
    headers['Accept'] = 'application/json';
    
    // Add default JWT token if not provided in request
    if (!headers['Authorization'] && process.env.JWT_TOKEN) {
      headers['Authorization'] = `Bearer ${process.env.JWT_TOKEN}`;
    }

    // Ensure X-Ekyc-Device-Info is preserved if it exists
    // This is important because it contains the device ID generated on the client side
    if (!headers['X-Ekyc-Device-Info']) {
      // If not provided, generate a fallback UUID for server-side requests
      const uuid = UuidGenerator.getUniqueId();
      headers['X-Ekyc-Device-Info'] = `browser|${uuid}`;
    }

    // Make the GET request with headers only (no body)
    const response = await fetch(url, {
      method: 'GET',
      headers: headers,
    });

    // Get the response data
    const data = await response.json();

    // Return the response with the same status code
    return res.status(response.status).json(data);
  } catch (error) {
    console.error('Error getting session token:', error);
    return res.status(500).json({ error: 'Failed to get session token: ',error });
  }
}

// Export for both CommonJS and ES modules
module.exports = handler;
module.exports.default = handler;
