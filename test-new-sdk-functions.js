/**
 * Test script for the new 5 main SDK entry point functions
 * This script tests the implementation without requiring a full build
 */

console.log('🧪 Testing New SDK Functions Implementation...\n');

try {
  // Test importing the SDK entry point
  console.log('1. Testing SDK entry point import...');
  const EkycSDK = require('./lib/sdk-entry');
  console.log('✅ SDK entry point imported successfully');
  console.log('📋 Available functions:', Object.keys(EkycSDK));

  // Test that the 5 main functions exist
  console.log('\n2. Testing 5 main SDK functions exist...');
  const requiredFunctions = [
    'initEkyc',
    'ocrIdCard', 
    'ocrIdCardVerifyByFace',
    'ndidVerification',
    'livenessCheck'
  ];

  let allFunctionsExist = true;
  requiredFunctions.forEach(funcName => {
    if (typeof EkycSDK[funcName] === 'function') {
      console.log(`✅ ${funcName} - exists and is a function`);
    } else {
      console.log(`❌ ${funcName} - missing or not a function`);
      allFunctionsExist = false;
    }
  });

  if (allFunctionsExist) {
    console.log('✅ All 5 main SDK functions are properly defined');
  } else {
    console.log('❌ Some SDK functions are missing');
  }

  // Test function signatures (basic validation)
  console.log('\n3. Testing function signatures...');
  
  // Test initEkyc with invalid parameters
  console.log('Testing initEkyc parameter validation...');
  try {
    await EkycSDK.initEkyc({});
    console.log('❌ initEkyc should have thrown error for missing sessionId');
  } catch (error) {
    if (error.message.includes('sessionId is required')) {
      console.log('✅ initEkyc properly validates sessionId parameter');
    } else {
      console.log('⚠️ initEkyc validation error:', error.message);
    }
  }

  // Test ndidVerification with invalid parameters
  console.log('Testing ndidVerification parameter validation...');
  try {
    await EkycSDK.ndidVerification({});
    console.log('❌ ndidVerification should have thrown error for missing parameters');
  } catch (error) {
    if (error.message.includes('identifierType is required')) {
      console.log('✅ ndidVerification properly validates identifierType parameter');
    } else {
      console.log('⚠️ ndidVerification validation error:', error.message);
    }
  }

  // Test SDK metadata
  console.log('\n4. Testing SDK metadata...');
  console.log(`📦 SDK Name: ${EkycSDK.name}`);
  console.log(`🔢 SDK Version: ${EkycSDK.version}`);
  
  if (EkycSDK.name === 'SCB TechX eKYC SDK' && EkycSDK.version === '1.0.0') {
    console.log('✅ SDK metadata is correct');
  } else {
    console.log('⚠️ SDK metadata may need updating');
  }

  // Test namespaced APIs still exist
  console.log('\n5. Testing existing namespaced APIs...');
  const namespacedAPIs = ['Auth', 'FaceTec', 'Utils'];
  namespacedAPIs.forEach(apiName => {
    if (EkycSDK[apiName] && typeof EkycSDK[apiName] === 'object') {
      console.log(`✅ ${apiName} namespace exists`);
    } else {
      console.log(`❌ ${apiName} namespace missing`);
    }
  });

  console.log('\n🎉 SDK Function Implementation Test Complete!');
  console.log('✅ All new functions are properly implemented and integrated');

} catch (error) {
  console.error('❌ Test failed with error:', error);
  console.error('Stack trace:', error.stack);
}
