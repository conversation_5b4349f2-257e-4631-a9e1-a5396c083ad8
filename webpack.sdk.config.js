const path = require('path');
const CopyPlugin = require('copy-webpack-plugin');

module.exports = {
  mode: 'production',
  
  // Multiple entry points for different use cases
  entry: {
    // Main SDK bundle - for browser usage
    'ekyc-sdk': './lib/sdk-entry.js',
    
    // Server-side API handlers bundle
    'ekyc-api': './lib/infrastructure/api/index.js',
    
    // Individual API handlers for granular imports
    'api/session-token': './lib/infrastructure/api/session-token.js',
    'api/facetec-session-token': './lib/infrastructure/api/facetec-session-token.js',
    'api/idscan-only': './lib/infrastructure/api/idscan-only.js',
    'api/health': './lib/infrastructure/api/health.js',
    
    // Utilities bundle
    'utils': './lib/infrastructure/utils/index.js',
    
    // FaceTec integration bundle
    'facetec': './lib/facetec-entry.js'
  },
  
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: '[name].js',
    libraryTarget: 'umd',
    library: {
      name: ['EkycSDK', '[name]'],
      type: 'umd'
    },
    umdNamedDefine: true,
    globalObject: 'this',
    clean: true // Clean dist folder before build
  },
  
  // Optimization for better developer experience
  optimization: {
    minimize: false, // Keep code readable for debugging
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
      },
    },
  },
  
  // Copy static assets
  plugins: [
    new CopyPlugin({
      patterns: [
        {
          from: 'lib/core-sdk',
          to: 'core-sdk',
          globOptions: {
            ignore: ['**/*.d.ts', '**/*.map']
          }
        }
      ],
    }),
  ],
  
  resolve: {
    extensions: ['.js'],
    modules: [
      path.resolve(__dirname, 'lib'),
      'node_modules'
    ],
    fallback: {
      fs: false,
      path: false,
      crypto: false
    }
  },
  
  externals: {
    './core-sdk/FaceTecSDK.js/FaceTecSDK': 'FaceTecSDK'
  },
  
  // Enable source maps for debugging
  devtool: 'source-map'
};
